const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestAssessment() {
  try {
    // Get the current user (<EMAIL>)
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      console.error('User not found');
      return;
    }

    console.log('Found user:', user.email);

    // Check if assessment already exists
    const existingAssessment = await prisma.assessment.findFirst({
      where: { userId: user.id }
    });

    if (existingAssessment) {
      console.log('Assessment already exists, updating to COMPLETED...');
      
      // Update existing assessment to COMPLETED
      const updatedAssessment = await prisma.assessment.update({
        where: { id: existingAssessment.id },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          currentStep: 6
        }
      });

      console.log('Assessment updated:', updatedAssessment.id, updatedAssessment.status);
    } else {
      console.log('Creating new completed assessment...');
      
      // Create a new completed assessment
      const assessment = await prisma.assessment.create({
        data: {
          userId: user.id,
          status: 'COMPLETED',
          currentStep: 6,
          completedAt: new Date()
        }
      });

      console.log('Assessment created:', assessment.id, assessment.status);

      // Add some sample responses
      const sampleResponses = [
        { questionKey: 'jobDissatisfactionTriggers', answerValue: ['Poor Work-Life Balance'] },
        { questionKey: 'currentEmploymentStatus', answerValue: 'Employed Full-Time' },
        { questionKey: 'workExperience', answerValue: '6-10 years (Mid-Career)' },
        { questionKey: 'financialComfort', answerValue: 3 },
        { questionKey: 'workLifeBalanceImportance', answerValue: 'Very Important' }
      ];

      for (const response of sampleResponses) {
        await prisma.assessmentResponse.create({
          data: {
            assessmentId: assessment.id,
            questionKey: response.questionKey,
            answerValue: response.answerValue
          }
        });
      }

      console.log('Sample responses added');
    }

    console.log('✅ Test assessment setup complete!');
  } catch (error) {
    console.error('Error creating test assessment:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestAssessment();
