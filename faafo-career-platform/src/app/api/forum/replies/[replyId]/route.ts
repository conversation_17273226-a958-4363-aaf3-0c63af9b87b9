// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
import { isUserAdmin } from '@/lib/admin';

interface ForumReplyUpdateResponse {
  id: string;
  content: string;
  authorId: string;
  postId: string;
  createdAt: Date;
  updatedAt: Date;
  author: any;
}

// GET - Retrieve specific forum reply
export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ replyId: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 100 },
    async () => {
      const { replyId } = await params;
      const session = await getServerSession(authOptions);
      const userId = session?.user?.id;

      if (!replyId) {
        return NextResponse.json({
          success: false,
          error: 'Reply ID is required'
        }, { status: 400 });
      }

      const reply = await prisma.forumReply.findUnique({
        where: { 
          id: replyId,
          isHidden: false // Only show visible replies
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              profile: {
                select: {
                  profilePictureUrl: true,
                  forumReputation: true,
                },
              },
            },
          },
          _count: {
            select: {
              reactions: true,
            },
          },
          reactions: userId ? {
            where: {
              userId: userId,
            },
            select: {
              type: true,
            },
          } : {
            select: {
              type: true,
              userId: true,
            },
          },
        },
      });

      if (!reply) {
        return NextResponse.json({
          success: false,
          error: 'Reply not found'
        }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        data: reply
      });
    }
  );
});

// PUT - Update forum reply
export const PUT = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ replyId: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 },
    async () => {
      const session = await getServerSession(authOptions);
      
      if (!session?.user?.id) {
        return NextResponse.json({
          success: false,
          error: 'Authentication required'
        }, { status: 401 });
      }

      const { replyId } = await params;
      const body = await request.json();
      const { content } = body;

      if (!content?.trim()) {
        return NextResponse.json({
          success: false,
          error: 'Content is required'
        }, { status: 400 });
      }

      if (content.length > 2000) {
        return NextResponse.json({
          success: false,
          error: 'Reply content must be 2000 characters or less'
        }, { status: 400 });
      }

      // Check if user owns the reply
      const existingReply = await prisma.forumReply.findUnique({
        where: { id: replyId },
        select: { authorId: true }
      });

      if (!existingReply) {
        return NextResponse.json({
          success: false,
          error: 'Reply not found'
        }, { status: 404 });
      }

      if (existingReply.authorId !== session.user.id) {
        return NextResponse.json({
          success: false,
          error: 'You can only edit your own replies'
        }, { status: 403 });
      }

      // Update the reply
      const updatedReply = await prisma.forumReply.update({
        where: { id: replyId },
        data: {
          content: content.trim(),
          updatedAt: new Date(),
        },
        include: {
          author: {
            select: {
              id: true,
              email: true,
              name: true,
              profile: {
                select: {
                  profilePictureUrl: true,
                  forumReputation: true,
                  forumPostCount: true,
                  forumReplyCount: true,
                  currentCareerPath: true,
                  progressLevel: true,
                },
              },
            },
          },
        },
      });

      return NextResponse.json({
        success: true,
        data: updatedReply,
        message: 'Reply updated successfully'
      });
    }
  );
});

// DELETE - Delete forum reply
export const DELETE = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ replyId: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 5 },
    async () => {
      const session = await getServerSession(authOptions);
      
      if (!session?.user?.id) {
        return NextResponse.json({
          success: false,
          error: 'Authentication required'
        }, { status: 401 });
      }

      const { replyId } = await params;

      // Check if user owns the reply or is admin
      const existingReply = await prisma.forumReply.findUnique({
        where: { id: replyId },
        select: {
          authorId: true
        }
      });

      if (!existingReply) {
        return NextResponse.json({
          success: false,
          error: 'Reply not found'
        }, { status: 404 });
      }

      const isAdmin = await isUserAdmin(session.user.id);
      const isOwner = existingReply.authorId === session.user.id;

      if (!isOwner && !isAdmin) {
        return NextResponse.json({
          success: false,
          error: 'You can only delete your own replies'
        }, { status: 403 });
      }

      // Soft delete by hiding the reply
      await prisma.forumReply.update({
        where: { id: replyId },
        data: { 
          isHidden: true,
          updatedAt: new Date(),
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Reply deleted successfully'
      });
    }
  );
});
