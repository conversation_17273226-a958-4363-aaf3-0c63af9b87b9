import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { log } from '@/lib/logger';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf-protection';
import { withRateLimit } from '@/lib/rate-limiting';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

interface DeleteAccountRequest {
  confirmationText: string;
  reason?: string;
}

interface DeleteAccountResponse {
  success: true;
  message: string;
  deletedAt: string;
}

/**
 * DELETE /api/user/delete
 * Permanently delete user account and all associated data (GDPR compliant)
 */
export const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 3 }, // Very strict rate limiting for account deletion
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
          const error = new Error('Not authenticated') as any;
          error.statusCode = 401;
          throw error;
        }

        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          include: {
            profile: true,
            assessmentResults: true,
            freedomFundGoals: true,
            forumPosts: true,
            forumReplies: true,
            forumReactions: true,
            bookmarkedResources: true,
            bookmarkedCareerPaths: true,
          },
        });

        if (!user) {
          const error = new Error('User not found') as any;
          error.statusCode = 404;
          throw error;
        }

        // Parse request body
        const { confirmationText, reason }: DeleteAccountRequest = await request.json();

        // Validate confirmation text
        if (confirmationText !== 'DELETE MY ACCOUNT') {
          const error = new Error('Invalid confirmation text. Please type "DELETE MY ACCOUNT" exactly.') as any;
          error.statusCode = 400;
          throw error;
        }

        log.info('Account deletion initiated', {
          component: 'user_delete_api',
          userId: user.id,
          userEmail: user.email,
          reason: reason || 'No reason provided'
        });

        // Start transaction to ensure all data is deleted atomically
        const deletedAt = new Date();
        
        await prisma.$transaction(async (tx) => {
          // Delete in order to respect foreign key constraints
          
          // 1. Delete forum reactions
          await tx.forumReaction.deleteMany({
            where: { userId: user.id }
          });

          // 2. Delete forum replies
          await tx.forumReply.deleteMany({
            where: { userId: user.id }
          });

          // 3. Delete forum posts
          await tx.forumPost.deleteMany({
            where: { userId: user.id }
          });

          // 4. Delete bookmarks
          await tx.bookmarkedResource.deleteMany({
            where: { userId: user.id }
          });

          await tx.bookmarkedCareerPath.deleteMany({
            where: { userId: user.id }
          });

          // 5. Delete freedom fund goals
          await tx.freedomFundGoal.deleteMany({
            where: { userId: user.id }
          });

          // 6. Delete assessment results
          await tx.assessmentResult.deleteMany({
            where: { userId: user.id }
          });

          // 7. Delete profile
          if (user.profile) {
            await tx.profile.delete({
              where: { userId: user.id }
            });
          }

          // 8. Delete user sessions
          await tx.session.deleteMany({
            where: { userId: user.id }
          });

          // 9. Delete accounts (OAuth connections)
          await tx.account.deleteMany({
            where: { userId: user.id }
          });

          // 10. Finally delete the user
          await tx.user.delete({
            where: { id: user.id }
          });

          // 11. Log the deletion for audit purposes
          log.info('Account deletion completed', {
            component: 'user_delete_api',
            userId: user.id,
            userEmail: user.email,
            deletedAt: deletedAt.toISOString(),
            reason: reason || 'No reason provided'
          });
        });

        const response: DeleteAccountResponse = {
          success: true,
          message: 'Your account and all associated data have been permanently deleted.',
          deletedAt: deletedAt.toISOString()
        };

        return NextResponse.json(response);
      }
    );
  });
});
