import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { log } from '@/lib/logger';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf-protection';
import { withRateLimit } from '@/lib/rate-limiting';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

interface UserExportData {
  user: {
    id: string;
    email: string;
    name?: string;
    createdAt: string;
    updatedAt: string;
  };
  profile?: any;
  assessmentResults: any[];
  freedomFundGoals: any[];
  forumPosts: any[];
  forumReplies: any[];
  forumReactions: any[];
  bookmarkedResources: any[];
  bookmarkedCareerPaths: any[];
  exportedAt: string;
  exportFormat: string;
}

/**
 * GET /api/user/export?format=json|csv
 * Export all user data in GDPR-compliant format
 */
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 60 * 60 * 1000, maxRequests: 5 }, // 5 exports per hour
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
          const error = new Error('Not authenticated') as any;
          error.statusCode = 401;
          throw error;
        }

        const { searchParams } = new URL(request.url);
        const format = searchParams.get('format') || 'json';

        if (!['json', 'csv'].includes(format)) {
          const error = new Error('Invalid format. Supported formats: json, csv') as any;
          error.statusCode = 400;
          throw error;
        }

        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          include: {
            profile: true,
            assessmentResults: true,
            freedomFundGoals: true,
            forumPosts: {
              include: {
                replies: true,
                reactions: true,
              }
            },
            forumReplies: {
              include: {
                reactions: true,
              }
            },
            forumReactions: true,
            bookmarkedResources: {
              include: {
                resource: true,
              }
            },
            bookmarkedCareerPaths: {
              include: {
                careerPath: true,
              }
            },
          },
        });

        if (!user) {
          const error = new Error('User not found') as any;
          error.statusCode = 404;
          throw error;
        }

        log.info('User data export initiated', {
          component: 'user_export_api',
          userId: user.id,
          userEmail: user.email,
          format
        });

        // Prepare export data
        const exportData: UserExportData = {
          user: {
            id: user.id,
            email: user.email,
            name: user.name || undefined,
            createdAt: user.createdAt.toISOString(),
            updatedAt: user.updatedAt.toISOString(),
          },
          profile: user.profile ? {
            ...user.profile,
            createdAt: user.profile.createdAt.toISOString(),
            updatedAt: user.profile.updatedAt.toISOString(),
            careerInterests: user.profile.careerInterests ? JSON.parse(user.profile.careerInterests) : [],
            skillsToLearn: user.profile.skillsToLearn ? JSON.parse(user.profile.skillsToLearn) : [],
          } : undefined,
          assessmentResults: user.assessmentResults.map(result => ({
            ...result,
            createdAt: result.createdAt.toISOString(),
            updatedAt: result.updatedAt.toISOString(),
            responses: result.responses ? JSON.parse(result.responses) : {},
            results: result.results ? JSON.parse(result.results) : {},
          })),
          freedomFundGoals: user.freedomFundGoals.map(goal => ({
            ...goal,
            createdAt: goal.createdAt.toISOString(),
            updatedAt: goal.updatedAt.toISOString(),
          })),
          forumPosts: user.forumPosts.map(post => ({
            ...post,
            createdAt: post.createdAt.toISOString(),
            updatedAt: post.updatedAt.toISOString(),
            replies: post.replies.map(reply => ({
              ...reply,
              createdAt: reply.createdAt.toISOString(),
              updatedAt: reply.updatedAt.toISOString(),
            })),
            reactions: post.reactions.map(reaction => ({
              ...reaction,
              createdAt: reaction.createdAt.toISOString(),
            })),
          })),
          forumReplies: user.forumReplies.map(reply => ({
            ...reply,
            createdAt: reply.createdAt.toISOString(),
            updatedAt: reply.updatedAt.toISOString(),
            reactions: reply.reactions.map(reaction => ({
              ...reaction,
              createdAt: reaction.createdAt.toISOString(),
            })),
          })),
          forumReactions: user.forumReactions.map(reaction => ({
            ...reaction,
            createdAt: reaction.createdAt.toISOString(),
          })),
          bookmarkedResources: user.bookmarkedResources.map(bookmark => ({
            ...bookmark,
            createdAt: bookmark.createdAt.toISOString(),
            resource: bookmark.resource,
          })),
          bookmarkedCareerPaths: user.bookmarkedCareerPaths.map(bookmark => ({
            ...bookmark,
            createdAt: bookmark.createdAt.toISOString(),
            careerPath: bookmark.careerPath,
          })),
          exportedAt: new Date().toISOString(),
          exportFormat: format,
        };

        if (format === 'json') {
          return NextResponse.json(exportData, {
            headers: {
              'Content-Disposition': `attachment; filename="faafo-user-data-${user.id}-${Date.now()}.json"`,
              'Content-Type': 'application/json',
            },
          });
        } else if (format === 'csv') {
          // Convert to CSV format (simplified)
          const csvData = convertToCSV(exportData);
          return new NextResponse(csvData, {
            headers: {
              'Content-Disposition': `attachment; filename="faafo-user-data-${user.id}-${Date.now()}.csv"`,
              'Content-Type': 'text/csv',
            },
          });
        }

        const error = new Error('Unsupported format') as any;
        error.statusCode = 400;
        throw error;
      }
    );
  });
});

function convertToCSV(data: UserExportData): string {
  const lines: string[] = [];
  
  // Add header
  lines.push('Section,Field,Value,Date');
  
  // User data
  lines.push(`User,ID,${data.user.id},${data.user.createdAt}`);
  lines.push(`User,Email,${data.user.email},${data.user.createdAt}`);
  if (data.user.name) lines.push(`User,Name,${data.user.name},${data.user.createdAt}`);
  
  // Profile data
  if (data.profile) {
    Object.entries(data.profile).forEach(([key, value]) => {
      if (key !== 'createdAt' && key !== 'updatedAt' && value !== null && value !== undefined) {
        lines.push(`Profile,${key},"${String(value).replace(/"/g, '""')}",${data.profile.updatedAt}`);
      }
    });
  }
  
  // Assessment results
  data.assessmentResults.forEach((result, index) => {
    lines.push(`Assessment ${index + 1},Score,${result.score || 'N/A'},${result.createdAt}`);
    lines.push(`Assessment ${index + 1},Status,${result.status || 'N/A'},${result.createdAt}`);
  });
  
  // Freedom Fund goals
  data.freedomFundGoals.forEach((goal, index) => {
    lines.push(`Freedom Fund Goal ${index + 1},Target Amount,${goal.targetAmount || 'N/A'},${goal.createdAt}`);
    lines.push(`Freedom Fund Goal ${index + 1},Current Amount,${goal.currentAmount || 'N/A'},${goal.createdAt}`);
  });
  
  // Forum activity summary
  lines.push(`Forum Activity,Posts Count,${data.forumPosts.length},${data.exportedAt}`);
  lines.push(`Forum Activity,Replies Count,${data.forumReplies.length},${data.exportedAt}`);
  lines.push(`Forum Activity,Reactions Count,${data.forumReactions.length},${data.exportedAt}`);
  
  // Bookmarks summary
  lines.push(`Bookmarks,Resources Count,${data.bookmarkedResources.length},${data.exportedAt}`);
  lines.push(`Bookmarks,Career Paths Count,${data.bookmarkedCareerPaths.length},${data.exportedAt}`);
  
  return lines.join('\n');
}
