"use client";

import React, { useState, useCallback } from 'react';
import { z } from 'zod';
import { useCSRF } from '@/hooks/useCSRF';
import { useValidatedForm } from '@/hooks/useFormValidation';
import { FormValidationRules, signupSchema } from '@/lib/client-validation';

const SignupForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [formMessage, setFormMessage] = useState<{ type: 'info' | 'success' | 'error'; content: string } | null>(null);
  const [validationErrors, setValidationErrors] = useState<{email?: string; password?: string}>({});
  const [isRegistered, setIsRegistered] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const { getHeaders, isLoading: csrfLoading } = useCSRF();

  // Validation function
  const validateForm = useCallback(() => {
    try {
      signupSchema.parse({ email, password });
      setValidationErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: {email?: string; password?: string} = {};
        error.errors.forEach((err) => {
          if (err.path[0] === 'email') {
            errors.email = err.message;
          } else if (err.path[0] === 'password') {
            errors.password = err.message;
          }
        });
        setValidationErrors(errors);
      }
      return false;
    }
  }, [email, password]);

  const handleSubmit = useCallback(async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setValidationErrors({});

    // Client-side validation
    if (!validateForm()) {
      return;
    }

    setFormMessage({ type: 'info', content: 'Signing up...' });

    try {
      const response = await fetch('/api/signup', {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ email, password }),
      });

      // Check if the response is JSON before parsing
      const contentType = response.headers.get('content-type');
      let data: { message: string; requiresVerification?: boolean } = { message: 'An unexpected error occurred' }; // Default error message

      if (contentType && contentType.includes('application/json')) {
        try {
          data = await response.json();
        } catch (jsonError) {
          console.error('Failed to parse JSON response:', jsonError);
          // If JSON parsing fails, data remains the default error message
        }
      } else {
        // If response is not JSON, try to read as text
        const textResponse = await response.text();
        console.error('Non-JSON response received:', textResponse);
        data.message = 'Received non-JSON response from server.';
      }

      if (response.ok) {
        setFormMessage({ type: 'success', content: data.message });
        if (data.requiresVerification) {
          setIsRegistered(true);
        }
      } else {
        setFormMessage({ type: 'error', content: 'Error: ' + (data.message || 'An unexpected error occurred') });
      }
    } catch (error) {
      console.error('Signup error:', error);
      setFormMessage({ type: 'error', content: 'Error: An unexpected error occurred.' });
    }
  }, [email, password, getHeaders, validateForm]);

  const handleResendVerification = useCallback(async () => {
    setIsResending(true);
    setFormMessage({ type: 'info', content: 'Sending verification email...' });

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setFormMessage({ type: 'success', content: data.data?.message || data.message || 'Verification email sent successfully.' });
      } else {
        // Handle different error response formats from unified error handler
        const errorMessage = data.error?.message || data.error || data.message || 'Failed to send verification email';
        setFormMessage({ type: 'error', content: 'Error: ' + (typeof errorMessage === 'string' ? errorMessage : 'Failed to send verification email') });
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      setFormMessage({ type: 'error', content: 'Error: An unexpected error occurred.' });
    } finally {
      setIsResending(false);
    }
  }, [email, getHeaders]);

  // Show verification success message if user just registered
  if (isRegistered) {
    return (
      <div className="mt-8 w-full max-w-sm">
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                Registration Successful!
              </h3>
              <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                <p>
                  We've sent a verification email to <strong>{email}</strong>.
                  Please check your inbox and click the verification link to activate your account.
                </p>
              </div>
              <div className="mt-4 space-y-2">
                <button
                  onClick={handleResendVerification}
                  disabled={isResending}
                  className="text-sm text-green-800 dark:text-green-200 underline hover:text-green-600 dark:hover:text-green-400 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isResending ? 'Sending...' : 'Resend verification email'}
                </button>
                <div>
                  <button
                    onClick={() => {
                      setIsRegistered(false);
                      setEmail('');
                      setPassword('');
                      setFormMessage(null);
                    }}
                    className="text-sm text-gray-600 dark:text-gray-400 underline hover:text-gray-800 dark:hover:text-gray-200"
                  >
                    Register a different email
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        {formMessage && (
          <div className={`mt-4 p-3 rounded ${
            formMessage.type === 'success' ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300' :
            formMessage.type === 'error' ? 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300' :
            'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
          }`}>
            {formMessage.content}
          </div>
        )}
      </div>
    );
  }

  return (
    <form 
      onSubmit={handleSubmit} 
      className="mt-8 w-full max-w-sm"
      aria-describedby={formMessage ? "signup-form-message" : undefined}
    >
      <div className="mb-4">
        <label htmlFor="email" className="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">Email Address</label>
        <input
          type="email"
          id="email"
          name="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className={`appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-gray-100 dark:placeholder-gray-500 leading-tight ${
            validationErrors.email
              ? 'border-red-500'
              : 'border-gray-200 dark:border-gray-700'
          }`}
          placeholder="Enter your email"
          autoComplete="email"
          required
          maxLength={254}
        />
        {validationErrors.email && (
          <div className="text-red-500 text-sm mt-1">{validationErrors.email}</div>
        )}
      </div>
      <div className="mb-6">
        <label htmlFor="password" className="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">Password</label>
        <input
          type="password"
          id="password"
          name="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className={`appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-gray-100 dark:placeholder-gray-500 leading-tight ${
            validationErrors.password
              ? 'border-red-500'
              : 'border-gray-200 dark:border-gray-700'
          }`}
          placeholder="Create a password"
          autoComplete="new-password"
          required
          maxLength={128}
        />
        {validationErrors.password && (
          <div className="text-red-500 text-sm mt-1">{validationErrors.password}</div>
        )}
      </div>
      <div className="flex items-center justify-between">
        <button
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={csrfLoading}
        >
          {csrfLoading ? 'Loading...' : 'Sign Up'}
        </button>
      </div>
      <div className="mt-4 text-center text-xs text-gray-500 dark:text-gray-400">
        By signing up, you agree to our <a href="/terms-of-service" className="underline hover:text-blue-600">Terms of Service</a> and <a href="/privacy-policy" className="underline hover:text-blue-600">Privacy Policy</a>.
      </div>
      {formMessage && formMessage.type === 'error' && (
        <p
          id="signup-form-message"
          className="mt-4 text-center text-sm text-red-600"
          role="alert"
        >
          {formMessage.content}
        </p>
      )}
      {formMessage && (formMessage.type === 'success' || formMessage.type === 'info') && (
        <p
          id="signup-form-message"
          className={`mt-4 text-center text-sm ${ 
            formMessage.type === 'success' ? 'text-green-600' : 'text-blue-600'
          }`}
          role="status"
        >
          {formMessage.content}
        </p>
      )}
    </form>
  );
};

export default SignupForm; 