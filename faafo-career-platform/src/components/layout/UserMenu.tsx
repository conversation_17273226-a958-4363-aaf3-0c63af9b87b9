'use client';

import React from 'react';
import Link from 'next/link';
import { signIn, signOut } from 'next-auth/react';
import { User, LogOut, LogIn, UserPlus, BarChart3, MessageSquare, Settings } from 'lucide-react';
import { useAuthStatus } from '@/hooks/useAuthState';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { ClientOnly } from '@/components/common/ClientOnly';

interface UserMenuProps {
  variant?: 'desktop' | 'mobile';
  className?: string;
  onItemClick?: () => void;
}

/**
 * Unified User Menu Component
 * 
 * Provides consistent user menu display across the application.
 * Shows appropriate menu items based on authentication state.
 */
export function UserMenu({ variant = 'desktop', className = '', onItemClick }: UserMenuProps) {
  const { isAuthenticated, user, isAdmin, isLoading } = useAuthStatus();
  const [mounted, setMounted] = React.useState(false);

  // Prevent hydration mismatch by only rendering after mount
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Show loading state until mounted and auth is resolved
  if (!mounted) {
    if (variant === 'mobile') {
      return (
        <div className="space-y-2" suppressHydrationWarning>
          <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-10 rounded"></div>
          <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-10 rounded"></div>
        </div>
      );
    }
    return (
      <div className="flex items-center space-x-2" suppressHydrationWarning>
        <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-8 w-16 rounded"></div>
        <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-8 w-16 rounded"></div>
      </div>
    );
  }

  // Show loading state for auth resolution
  if (isLoading) {
    if (variant === 'mobile') {
      return (
        <div className="space-y-2">
          <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-10 rounded"></div>
          <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-10 rounded"></div>
        </div>
      );
    }
    return (
      <div className="flex items-center space-x-2">
        <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-8 w-16 rounded"></div>
        <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-8 w-16 rounded"></div>
      </div>
    );
  }

  if (variant === 'mobile') {
    return (
      <ClientOnly
        fallback={
          <div className="space-y-2">
            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-10 rounded"></div>
            <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-10 rounded"></div>
          </div>
        }
      >
        <div className={`space-y-1 ${className}`}>
          {isAuthenticated ? (
          <>
            <Link
              href="/dashboard"
              onClick={onItemClick}
              className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-3" aria-hidden="true" />
                Dashboard
              </div>
            </Link>
            <Link
              href="/forum"
              onClick={onItemClick}
              className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div className="flex items-center">
                <MessageSquare className="h-5 w-5 mr-3" aria-hidden="true" />
                Forum
              </div>
            </Link>
            <Link
              href="/profile"
              onClick={onItemClick}
              className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div className="flex items-center">
                <User className="h-5 w-5 mr-3" aria-hidden="true" />
                Profile {user?.email && <span className="text-xs text-gray-500 ml-1">({user.email})</span>}
              </div>
            </Link>
            <Link
              href="/settings"
              onClick={onItemClick}
              className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div className="flex items-center">
                <Settings className="h-5 w-5 mr-3" aria-hidden="true" />
                Settings
              </div>
            </Link>
            <button
              onClick={() => {
                signOut();
                onItemClick?.();
              }}
              className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div className="flex items-center">
                <LogOut className="h-5 w-5 mr-3" aria-hidden="true" />
                Sign Out
              </div>
            </button>
          </>
        ) : (
          <>
            <button
              onClick={() => {
                signIn();
                onItemClick?.();
              }}
              className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div className="flex items-center">
                <LogIn className="h-5 w-5 mr-3" aria-hidden="true" />
                Log In
              </div>
            </button>
            <Link
              href="/signup"
              onClick={onItemClick}
              className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div className="flex items-center">
                <UserPlus className="h-5 w-5 mr-3" aria-hidden="true" />
                Sign Up
              </div>
            </Link>
          </>
        )}
        </div>
      </ClientOnly>
    );
  }

  // Desktop variant
  return (
    <ClientOnly
      fallback={
        <div className="flex items-center space-x-2">
          <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-8 w-16 rounded"></div>
          <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-8 w-16 rounded"></div>
        </div>
      }
    >
      <div className={`flex items-center space-x-2 ${className}`}>
        {isAuthenticated ? (
        <>
          <Link
            href="/dashboard"
            className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
            aria-label="Go to your dashboard"
          >
            <BarChart3 className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
            <span className="hidden xl:inline">Dashboard</span>
          </Link>
          <Link
            href="/forum"
            className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
            aria-label="Visit community forum"
          >
            <MessageSquare className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
            <span className="hidden xl:inline">Forum</span>
          </Link>
          <Link
            href="/profile"
            className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
            aria-label="View your profile"
            title={user?.email ? `Logged in as ${user.email}` : 'View your profile'}
          >
            <User className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
            <span className="hidden xl:inline">Profile</span>
          </Link>
          <button
            onClick={() => signOut()}
            className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
            aria-label="Sign out of your account"
            title={user?.email ? `Sign out ${user.email}` : 'Sign out'}
          >
            <LogOut className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
            <span className="hidden xl:inline">Sign Out</span>
          </button>
        </>
      ) : (
        <>
          <button
            onClick={() => signIn()}
            className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
            aria-label="Log in to your account"
          >
            <LogIn className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
            <span className="hidden xl:inline">Log In</span>
          </button>
          <Link
            href="/signup"
            className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
            aria-label="Create a new account"
          >
            <UserPlus className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
            <span className="hidden xl:inline">Sign Up</span>
          </Link>
        </>
      )}
      </div>
    </ClientOnly>
  );
}

/**
 * Compact User Menu for smaller spaces
 */
export function CompactUserMenu({ className = '' }: { className?: string }) {
  const { isAuthenticated, user, isLoading } = useAuthStatus();
  const [mounted, setMounted] = React.useState(false);

  // Prevent hydration mismatch by only rendering after mount
  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-8 w-8 rounded-full" suppressHydrationWarning></div>;
  }

  if (isLoading) {
    return <div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-8 w-8 rounded-full"></div>;
  }

  return (
    <ClientOnly
      fallback={<div className="animate-pulse bg-gray-300 dark:bg-gray-600 h-8 w-8 rounded-full"></div>}
    >
      {!isAuthenticated ? (
        <div className={`flex items-center space-x-2 ${className}`}>
          <Button variant="ghost" size="sm" onClick={() => signIn()}>
            Log In
          </Button>
          <Button asChild size="sm">
            <Link href="/signup">Sign Up</Link>
          </Button>
        </div>
      ) : (
        <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative h-8 w-8 rounded-full">
          <User className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user?.name || 'User'}</p>
            <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/dashboard" className="w-full">Dashboard</Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/profile" className="w-full">Profile</Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/forum" className="w-full">Forum</Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/settings" className="w-full">Settings</Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => signOut()}>
          Sign Out
        </DropdownMenuItem>
      </DropdownMenuContent>
        </DropdownMenu>
      )}
    </ClientOnly>
  );
}
