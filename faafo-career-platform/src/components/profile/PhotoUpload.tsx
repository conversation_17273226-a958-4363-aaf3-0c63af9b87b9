'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Upload, X, Camera, User, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PhotoUploadProps {
  currentPhotoUrl?: string;
  onPhotoUpdate: (photoUrl: string | null) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
}

const sizeClasses = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24',
  lg: 'w-32 h-32'
};

export default function PhotoUpload({
  currentPhotoUrl,
  onPhotoUpdate,
  className,
  size = 'lg',
  disabled = false
}: PhotoUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback(async (file: File) => {
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setUploadError('Please select a JPEG, PNG, or WebP image.');
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      setUploadError('File size must be less than 5MB.');
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/profile/photo', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || result.message || 'Failed to upload photo');
      }

      // Handle both direct response and nested data response formats
      const photoUrl = result.profilePictureUrl || result.data?.profilePictureUrl;
      if (!photoUrl) {
        throw new Error('No photo URL returned from server');
      }

      onPhotoUpdate(photoUrl);
    } catch (error) {
      console.error('Photo upload error:', error);
      setUploadError(error instanceof Error ? error.message : String(error));
    } finally {
      setIsUploading(false);
    }
  }, [onPhotoUpdate]);

  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const handleRemovePhoto = useCallback(async () => {
    if (!currentPhotoUrl) return;

    setIsUploading(true);
    setUploadError(null);

    try {
      const response = await fetch('/api/profile/photo', {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || result.message || 'Failed to remove photo');
      }

      onPhotoUpdate(null);
    } catch (error) {
      console.error('Photo removal error:', error);
      setUploadError(error instanceof Error ? error.message : String(error));
    } finally {
      setIsUploading(false);
    }
  }, [currentPhotoUrl, onPhotoUpdate]);

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center space-x-4">
        {/* Avatar Display */}
        <div className="relative">
          <Avatar className={cn(sizeClasses[size], 'border-2 border-border')}>
            <AvatarImage src={currentPhotoUrl || undefined} alt="Profile photo" />
            <AvatarFallback className="bg-muted">
              <User className="w-1/2 h-1/2 text-muted-foreground" />
            </AvatarFallback>
          </Avatar>
          
          {isUploading && (
            <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
              <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
            </div>
          )}
        </div>

        {/* Upload Controls */}
        <div className="flex-1 space-y-2">
          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={openFileDialog}
              disabled={disabled || isUploading}
              className="flex items-center space-x-2"
            >
              <Camera className="w-4 h-4" />
              <span>{currentPhotoUrl ? 'Change Photo' : 'Upload Photo'}</span>
            </Button>

            {currentPhotoUrl && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleRemovePhoto}
                disabled={disabled || isUploading}
                className="flex items-center space-x-2 text-destructive hover:text-destructive"
              >
                <Trash2 className="w-4 h-4" />
                <span>Remove</span>
              </Button>
            )}
          </div>

          <p className="text-xs text-muted-foreground">
            JPEG, PNG, or WebP. Max 5MB. Recommended: 512x512px
          </p>
        </div>
      </div>

      {/* Drag & Drop Area */}
      <Card
        className={cn(
          'border-2 border-dashed transition-colors cursor-pointer',
          dragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={!disabled ? openFileDialog : undefined}
      >
        <CardContent className="flex flex-col items-center justify-center py-8 text-center">
          <Upload className="w-8 h-8 text-muted-foreground mb-2" />
          <p className="text-sm font-medium text-foreground mb-1">
            Drop your photo here, or click to browse
          </p>
          <p className="text-xs text-muted-foreground">
            JPEG, PNG, WebP up to 5MB
          </p>
        </CardContent>
      </Card>

      {/* Error Display */}
      {uploadError && (
        <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
          <div className="flex items-center space-x-2">
            <X className="w-4 h-4 text-destructive" />
            <p className="text-sm text-destructive">{uploadError}</p>
          </div>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/webp"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled || isUploading}
      />
    </div>
  );
}
