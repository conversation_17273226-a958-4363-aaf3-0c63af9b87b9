import { useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface UseBookmarkOptions {
  onSuccess?: (bookmarked: boolean) => void;
  onError?: (error: string) => void;
}

export function useBookmark(resourceId: string, options: UseBookmarkOptions = {}) {
  const { data: session } = useSession();
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [csrfToken, setCsrfToken] = useState<string | null>(null);

  // Fetch CSRF token
  const fetchCSRFToken = useCallback(async () => {
    try {
      const response = await fetch('/api/csrf-token');
      if (response.ok) {
        const data = await response.json();
        setCsrfToken(data.csrfToken);
        return data.csrfToken;
      }
    } catch (error) {
      console.error('Error fetching CSRF token:', error);
    }
    return null;
  }, []);

  // Check bookmark status
  const checkBookmarkStatus = useCallback(async () => {
    if (!session?.user || !resourceId) return;

    try {
      const response = await fetch(`/api/learning-progress?resourceId=${resourceId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setIsBookmarked(result.data.status === 'BOOKMARKED');
        }
      }
    } catch (error) {
      console.error('Error checking bookmark status:', error);
    }
  }, [session?.user, resourceId]);

  // Toggle bookmark
  const toggleBookmark = useCallback(async () => {
    if (!session?.user) {
      options.onError?.('Please sign in to bookmark resources');
      return;
    }

    if (!resourceId) {
      options.onError?.('Resource not found');
      return;
    }

    setIsLoading(true);

    try {
      // Ensure we have a CSRF token
      let token = csrfToken;
      if (!token) {
        token = await fetchCSRFToken();
        if (!token) {
          options.onError?.('Unable to process request. Please try again.');
          return;
        }
      }

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add CSRF token for non-GET requests
      if (token) {
        headers['X-CSRF-Token'] = token;
      }

      const response = await fetch('/api/learning-progress', {
        method: 'POST',
        headers,
        credentials: 'same-origin',
        body: JSON.stringify({
          resourceId,
          status: isBookmarked ? 'NOT_STARTED' : 'BOOKMARKED',
        }),
      });

      if (response.ok) {
        const newBookmarkedState = !isBookmarked;
        setIsBookmarked(newBookmarkedState);
        options.onSuccess?.(newBookmarkedState);
      } else {
        const errorData = await response.json();
        console.error('Bookmark error:', errorData);
        
        if (response.status === 401) {
          options.onError?.('Please sign in to bookmark resources');
        } else if (response.status === 403) {
          // CSRF token might be invalid, try to refresh it
          await fetchCSRFToken();
          options.onError?.('Security token expired. Please try again.');
        } else {
          options.onError?.('Failed to update bookmark. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error updating bookmark:', error);
      options.onError?.('Failed to update bookmark. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [session?.user, resourceId, isBookmarked, csrfToken, fetchCSRFToken, options]);

  return {
    isBookmarked,
    isLoading,
    toggleBookmark,
    checkBookmarkStatus,
    isAuthenticated: !!session?.user,
  };
}
